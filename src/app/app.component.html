<ion-app [class.platform-cordova]="isCordova" [class.platform-browser]="!isCordova" [class.mobile-ui]="isMobileUI">
  <ng-container *ngIf="shouldShowHeaderAndSidebar">
    <ion-header>
      <ion-toolbar color="primary">
        <ion-buttons slot="start">
          <ion-button fill="clear" color="light" (click)="facilitiesFromServer()">
            <ion-icon slot="start" name="business-outline"></ion-icon>
            <span *ngIf="facilityName">{{facilityName}}</span>
            <span *ngIf="!facilityName">Select Facility</span>
          </ion-button>
        </ion-buttons>

        <ion-title class="ion-text-center">
          Welcome, {{userName}}
        </ion-title>

        <ion-buttons slot="end">
          <ion-button (click)="serverCall()">
            <ion-icon name="sync-outline"></ion-icon>
          </ion-button>
          <ion-button *ngIf="!isAndroid" (click)="logoutToInitialPage()">
            <ion-icon name="log-out-outline"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <div class="app-container">
      <app-side-nav #sideNav></app-side-nav>

      <div class="content-container">
        <ion-router-outlet></ion-router-outlet>
      </div>
    </div>
  </ng-container>

  <!-- For login and other auth pages, just show the content without header and side nav -->
  <ng-container *ngIf="!shouldShowHeaderAndSidebar">
    <ion-router-outlet></ion-router-outlet>
  </ng-container>
</ion-app>
