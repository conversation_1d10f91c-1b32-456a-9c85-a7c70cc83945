import { Compo<PERSON>, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Z<PERSON>, ViewChild } from '@angular/core';
import { Router, NavigationEnd, Event } from '@angular/router';
import { Al<PERSON><PERSON>ontroller, LoadingController, ModalController, NavController, Platform } from '@ionic/angular';
import { SplashScreen } from '@awesome-cordova-plugins/splash-screen/ngx';
import { StatusBar } from '@awesome-cordova-plugins/status-bar/ngx';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { DataService } from './services/data.service';
import { Subscription } from 'rxjs';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { AppConstants } from './shared/app-constants';
import { FacilitySelectionService } from './services/facility-selection.service';
import { ConfigService } from './services/config.service';
import { filter, take } from 'rxjs/operators';
import { AndroidPermissions } from '@awesome-cordova-plugins/android-permissions/ngx';
import { SideNavComponent } from './components/side-nav/side-nav.component';
import { UserContextService } from './services/user-context.service';


@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
})
export class AppComponent implements OnInit, OnDestroy {
  @ViewChild('sideNav') sideNav!: SideNavComponent;

  public constants: AppConstants;
  public devicePlatform: string = '';
  private platformState: string = '';
  private subscriptions = new Subscription();
  public isAndroid: boolean = false;
  public isFacilityAvailable: boolean = false;
  public facilityName: string = '';
  public userName: string = '';
  public showHeaderAndSidebar: boolean = false;
  private permissions: Array<string> = [];
  public userFacility: string = '';
  public isCordova: boolean = false;
  public isMobileUI: boolean = false;

  // Login page and other auth-related pages that should not show header and sidebar
  private authPages: string[] = [
    '/login',
    '/contractor-login',
    '/mobile-login',
    '/mobile-home',
    '/permissions',
    '/settings',
    '/ssologin'
  ];

  constructor(
    private platform: Platform,
    private splashScreen: SplashScreen,
    private statusBar: StatusBar,
    private unviredSDK: UnviredCordovaSDK,
    public alertController: AlertController,
    public navCtrl: NavController,
    private dataService: DataService,
    public device: Device,
    private router: Router,
    private facilitySelectionService: FacilitySelectionService,
    private ngZone: NgZone,
    private configService: ConfigService,
    public modalController: ModalController,
    private androidPermissions: AndroidPermissions,
    public loadingController: LoadingController,
    private userContextService: UserContextService
  ) {
    this.constants = new AppConstants();
    // Detect if running in Cordova environment
    this.isCordova = !!(window as any).cordova || !!(window as any).PhoneGap || !!(window as any).phonegap;
    // Detect mobile UI - either Cordova or small screen
    this.isMobileUI = this.isCordova || window.innerWidth <= 768;

    this.initializeApp();

    // Subscribe to router events to determine when to show/hide header and sidebar
    this.subscriptions.add(
      this.router.events
        .pipe(filter((event: Event): event is NavigationEnd => event instanceof NavigationEnd))
        .subscribe(async (event: NavigationEnd) => {
          console.log('Router navigation to:', event.url);
          this.updateHeaderSidebarVisibility();

          // If navigating to a main page and user context is not loaded, try to load it
          if (this.showHeaderAndSidebar && (!this.userName || !this.facilityName)) {
            console.log('Navigation detected, refreshing user context...');
            await this.getUserContext();
          }
        })
    );

    // Subscribe to user context updates
    this.subscriptions.add(
      this.userContextService.getUserContextUpdated().subscribe(async (updated) => {
        if (updated) {
          console.log('User context update notification received');
          await this.getUserContext();
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  async ngOnInit() {
    // Set initial header/sidebar state based on current route
    this.updateHeaderSidebarVisibility();

    // Check platform is ready
    this.platformState = await this.platform.ready();
    console.log("ngoninit called in app component");

    if (this.platformState.length > 0) {
      this.splashScreen.hide();

      // Initialize the app
      if (
        !(
          window.location.href.includes('resetpassword') ||
          window.location.href.includes('saml-sso')
        )
      ) {
        await this.initializeApp();
        this.dataService.setInitialized(true);
      }

      // Make sure to get and set the facility name from user context
      // Wait for user context to be available before trying to read it
      await this.waitForUserContextAndLoad();
    } else {
      console.log('platform error');
    }

    if (this.devicePlatform !== 'browser') {
      // Hide Windows top-left UWP bcak button
      if (this.devicePlatform == 'windows') {
        // var currentView = Windows.UI.Core.SystemNavigationManager.getForCurrentView();
        // currentView.appViewBackButtonVisibility = Windows.UI.Core.AppViewBackButtonVisibility.collapsed;
      } else {
        // Android hardware back button
        this.platform.backButton.subscribe(async () => {
          if (this.router.url === '/mobile-login?type=0') {
              navigator['app'].exitApp();
          } else if (this.router.url === '/mobile-home') {
            try {
              const element = await this.modalController.getTop();
              if (element) {
                element.dismiss();
                return;
              } else {
              navigator['app'].exitApp();
              }
            } catch (error) {
              this.unviredSDK.logError('AppComponent', 'backbutton()', 'ERROR: ' + error);
              // this.loader.showToast(this.translate.instant("Back button not responded! Please try again"));
            }
          }
        });
      }
    }
  }

  async initializeApp() {
    this.platform.ready().then(() => {
      this.isAndroid = this.platform.is('android');

      // Configure status bar for mobile platforms
      if (this.platform.is('cordova')) {
        this.statusBar.backgroundColorByHexString('#ffffff');
        this.statusBar.styleDefault(); // For dark text
      }

      // Load facility info if available
      this.checkFacilityAvailability();
    });

    try {
      // Load configuration from config.json
      await this.configService.loadConfig().pipe(take(1)).toPromise();

      this.devicePlatform = this.device.platform;
      this.dataService.setDevicePlatform(this.devicePlatform)

      // Handle platform-specific routing for root URL
      const currentUrl = window.location.href;
      const isRootUrl = currentUrl.endsWith('/') || currentUrl.endsWith('/#') || currentUrl.endsWith('/#/');

      console.log('Platform detection:', {
        devicePlatform: this.devicePlatform,
        isCordova: this.isCordova,
        isRootUrl: isRootUrl,
        currentUrl: currentUrl
      });

      if (isRootUrl && this.devicePlatform !== 'browser') {
        // For mobile apps accessing root URL, redirect to mobile-login
        console.log('Mobile app detected at root URL, redirecting to mobile-login');
        this.router.navigate(['/mobile-login'], { replaceUrl: true });
        return;
      }

      // Only proceed with platform-specific logic if we're not on a special route
      const isSpecialRoute = window.location.href.includes('resetpassword') ||
                            window.location.href.includes('saml-sso') ||
                            window.location.href.includes('mobile-login') ||
                            window.location.href.includes('contractor-login') ||
                            window.location.href.includes('login') ||
                            window.location.href.includes('permissions');

      if (!isSpecialRoute) {
        if (this.platform.is("android")) {
          await this.showPermissionsAlert();
        } else {
          this.dataService.setInitialized(true);
          await this.dataService.initializeLoginProcess();
        }
      }
    } catch (error) {
      this.unviredSDK.logError(
        'AppComponent',
        'initializeApp()',
        'ERROR: ' + error
      );
    }
  }

  async showPermissionsAlert() {
    const permissions = [];
    if(parseInt(this.device.version) > 12){
      let storageReadMediaImages = await this.androidPermissions.checkPermission("android.permission.READ_MEDIA_IMAGES");
      if (storageReadMediaImages.hasPermission == false ) {
        permissions.push(this.constants.REQUIRED_PERMISSION.STORAGE);
      }

      let pushNotif = await this.androidPermissions.checkPermission("android.permission.POST_NOTIFICATIONS");
      if (pushNotif.hasPermission == false){
        permissions.push(this.constants.REQUIRED_PERMISSION.NOTIFICATION)
      }
    } else{
      let storageRead= await this.androidPermissions.checkPermission( this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE);
      let storageWrite = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE);
      if (storageRead.hasPermission == false ||  storageWrite.hasPermission == false ) {
        permissions.push(this.constants.REQUIRED_PERMISSION.STORAGE);
      }
    }
    let phoneState = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.READ_PHONE_STATE);
    if (phoneState.hasPermission == false) {
      permissions.push(this.constants.REQUIRED_PERMISSION.PHONE_STATE);
    }
    let cameraPermission = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.CAMERA);
    if (cameraPermission.hasPermission == false) {
      permissions.push(this.constants.REQUIRED_PERMISSION.CAMERA);
    }

    await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.ACCESS_FINE_LOCATION);
    await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.ACCESS_COARSE_LOCATION);

    if (permissions.length > 0) {
      this.router.navigate(['permissions']);
    } else {
      this.dataService.setInitialized(true);
      await this.dataService.initializeLoginProcess();
    }
  }
  async checkFacilityAvailability() {
    try {
      const facilityData = await this.dataService.getData('USER_CONTEXT_HEADER');
      if (facilityData && facilityData.length > 0) {
        this.isFacilityAvailable = !!facilityData[0]?.CURRENT_FACILITY;
        this.facilityName = facilityData[0]?.CURRENT_FACILITY_DESC || '';
        this.userName = facilityData[0]?.FIRST_NAME || '';
      }
    } catch (error) {
      console.error('Error checking facility:', error);
      this.isFacilityAvailable = false;
    }
  }

  async facilitiesFromServer() {
    try {
      await this.dataService.showBusyIndicator('Loading facilities...', 'dots');

      // Fetch the latest facilities from the server
      await this.dataService.getFacility();
      await this.dataService.dismissBusyIndicator();

      // Get current facilities from the database
      const facilityData = await this.dataService.getData('FACILITY_HEADER');
      if (!facilityData || facilityData.length === 0) {
        this.dataService.showAlertMessage('Warning', 'No facilities available.');
        return;
      }

      // Get the current facility for pre-selection
      let userContextResult = await this.dataService.getData('USER_CONTEXT_HEADER');
      const currentFacilityId = userContextResult?.[0]?.CURRENT_FACILITY || '';

      // Prepare the inputs for the alert dialog
      const inputs = facilityData.map(facility => ({
        name: 'facility',
        type: 'radio',
        label: facility.NAME || facility.FACILITY_ID,
        value: facility.FACILITY_ID,
        checked: facility.FACILITY_ID === currentFacilityId
      }));

      // Show the facility selection alert
      const alert = await this.alertController.create({
        header: 'Select Facility',
        cssClass: 'facility-alert',
        backdropDismiss: false,
        inputs,
        buttons: [
          {
            text: 'Cancel',
            role: 'cancel'
          },
          {
            text: 'OK',
            handler: async (selectedFacilityId) => {
              if (!selectedFacilityId) {
                return;
              }

              try {
                await this.dataService.showBusyIndicator('Switching facility...', 'dots');

                // Save the selected facility ID for later use
                this.dataService.setSelectedFacilityId(selectedFacilityId);

                // Find the selected facility in our data
                const selectedFacility = facilityData.find(f => f.FACILITY_ID === selectedFacilityId);
                const facilityName = selectedFacility?.NAME || selectedFacilityId;

                // Update USER_CONTEXT_HEADER table
                if (userContextResult && userContextResult.length > 0) {
                  const updateQuery = `UPDATE USER_CONTEXT_HEADER SET
                    CURRENT_FACILITY = '${selectedFacilityId}',
                    CURRENT_FACILITY_DESC = '${facilityName}'`;

                  await this.unviredSDK.dbExecuteStatement(updateQuery);

                  // Also update the local storage user context
                  const userContext = JSON.parse(localStorage.getItem('userContext') || '{}');
                  if (userContext) {
                    userContext.CURRENT_FACILITY = selectedFacilityId;
                    userContext.CURRENT_FACILITY_DESC = facilityName;
                    localStorage.setItem('userContext', JSON.stringify(userContext));
                  }
                }

                // Update UI
                this.facilityName = facilityName;
                this.isFacilityAvailable = true;

                // Download structures for the new facility
                const customData = {
                  STRUCTURE: [
                    {
                      STRUCTURE_HEADER: {
                        FACILITY_ID: selectedFacilityId,
                        DIVISION_ID: '',
                        TAG: '',
                        NAME: '',
                        CATEGORY: '',
                        STRUCT_TYPE: '',
                        STATUS: '',
                        P_MODE: '',
                      },
                    },
                  ],
                };

                // Get structures for the selected facility
                await this.dataService.getStructures(customData);

                // Download permits for the new facility
                await this.dataService.downloadPermits(null, null, true);

                // Download skills for the new facility (if applicable)
                await this.dataService.getAllSkillData();

                // IMPORTANT: Emit facility changed event BEFORE navigation
                // This ensures any active subscribers get notified
                this.facilitySelectionService.emitFacilityChanged();

                // Use a navigation technique that forces component recreation
                // This is critical for proper data reloading
                this.ngZone.run(() => {
                  // First navigate to a dummy route (with skipLocationChange to not affect browser history)
                  this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
                    // Then navigate to permits route
                    this.router.navigate(['/permits']).then(() => {
                      // Only dismiss the busy indicator after navigation is complete
                      setTimeout(() => {
                        this.dataService.dismissBusyIndicator();
                      }, 300);
                    });
                  });
                });
              } catch (error) {
                console.error('Error switching facility:', error);
                await this.dataService.dismissBusyIndicator();
                this.dataService.showAlertMessage('Error', 'Failed to switch facility.');
              }
            }
          }
        ]
      });

      await alert.present();
    } catch (error) {
      console.error('Error in facilitiesFromServer:', error);
      await this.dataService.dismissBusyIndicator();
      this.dataService.showAlertMessage('Error', 'Failed to load facilities.');
    }
  }

  // New method to load facility options for the selection dialog
  private async loadFacilityOptions() {
    try {
      const facilityResult = await this.unviredSDK.dbExecuteStatement(
        'SELECT * FROM FACILITY_HEADER'
      );

      if (facilityResult.type === ResultType.success && facilityResult.data?.length > 0) {
        // Use DESCRIPTION field for display
        return facilityResult.data.map(facility => ({
          name: facility.DESCRIPTION || facility.FACILITY_ID,
          type: 'radio',
          label: facility.DESCRIPTION || facility.FACILITY_ID,
          value: facility.FACILITY_ID,
          checked: facility.FACILITY_ID === this.getCurrentFacilityId()
        }));
      }
      return []; // Return empty array if no facilities found
    } catch (error) {
      console.error('Error loading facilities:', error);
      return [];
    }
  }

  // Helper to get current facility ID from localStorage or other source
  private getCurrentFacilityId(): string {
    try {
      const userContext = JSON.parse(localStorage.getItem('userContext') || '{}');
      return userContext?.USER_CONTEXT_HEADER?.CURRENT_FACILITY || '';
    } catch (error) {
      return '';
    }
  }

  // Update the selected facility in the database and user context
  private async updateSelectedFacility(facilityId: string) {
    try {
      // Update USER_CONTEXT_HEADER table with new facility
      const updateQuery = `UPDATE USER_CONTEXT_HEADER SET CURRENT_FACILITY = '${facilityId}'`;
      await this.unviredSDK.dbExecuteStatement(updateQuery);

      // Update local storage
      const userContext = JSON.parse(localStorage.getItem('userContext') || '{}');
      if (userContext?.USER_CONTEXT_HEADER) {
        userContext.USER_CONTEXT_HEADER.CURRENT_FACILITY = facilityId;
        localStorage.setItem('userContext', JSON.stringify(userContext));
      }

      this.isFacilityAvailable = true;
    } catch (error) {
      console.error('Error updating selected facility:', error);
    }
  }

  // Update the facility name in the UI immediately after selection
  private async updateFacilityNameDisplay(facilityId: string) {
    try {
      const query = `SELECT * FROM FACILITY_HEADER WHERE FACILITY_ID = '${facilityId}'`;
      const result = await this.unviredSDK.dbExecuteStatement(query);

      if (result.type === ResultType.success && result.data?.length > 0) {
        // Explicitly use DESCRIPTION field for the display name
        this.facilityName = result.data[0].DESCRIPTION || facilityId;
        console.log('Updated facility name to:', this.facilityName);
      } else {
        this.facilityName = facilityId; // Fallback to ID
      }
    } catch (error) {
      console.error('Error updating facility name display:', error);
      this.facilityName = facilityId; // Fallback to ID on error
    }
  }

  // New method to wait for user context data and load it
  async waitForUserContextAndLoad() {
    try {
      // First, try to get user context immediately
      await this.getUserContext();

      // If we didn't get user data, wait for it to become available
      if (!this.userName || !this.facilityName) {
        console.log('User context not immediately available, waiting...');

        // Wait up to 10 seconds for user context to become available
        const maxAttempts = 50; // 50 attempts * 200ms = 10 seconds
        let attempts = 0;

        while (attempts < maxAttempts && (!this.userName || !this.facilityName)) {
          await new Promise(resolve => setTimeout(resolve, 200)); // Wait 200ms
          await this.getUserContext();
          attempts++;
        }

        if (!this.userName || !this.facilityName) {
          console.warn('User context still not available after waiting');
        } else {
          console.log('User context loaded successfully after waiting');
        }
      }
    } catch (error) {
      console.error('Error waiting for user context:', error);
    }
  }

  async getUserContext() {
    try {
      let userContextResult = await this.dataService.getData('USER_CONTEXT_HEADER');
      if (userContextResult && userContextResult.length > 0) {
        this.userName = userContextResult[0].FIRST_NAME + ' ' + userContextResult[0].LAST_NAME;

        // Set facility name if available
        if (userContextResult[0].CURRENT_FACILITY_DESC) {
          this.facilityName = userContextResult[0].CURRENT_FACILITY_DESC;
        } else if (userContextResult[0].CURRENT_FACILITY) {
          // If only the facility ID is available, try to get the description
          await this.getFacilityDescription(userContextResult[0].CURRENT_FACILITY);
        }

        // Store user context for later use
        localStorage.setItem('userContext', JSON.stringify({ USER_CONTEXT_HEADER: userContextResult[0] }));
      } else {
        // Fallback: try to get user context from localStorage if database query fails
        console.log('No user context in database, checking localStorage...');
        this.loadUserContextFromLocalStorage();
      }
    } catch (error) {
      console.error('Error getting user context:', error);
      // Fallback: try to get user context from localStorage
      this.loadUserContextFromLocalStorage();
    }
  }

  // Fallback method to load user context from localStorage
  private loadUserContextFromLocalStorage() {
    try {
      const userContextStr = localStorage.getItem('userContext');
      if (userContextStr) {
        const userContext = JSON.parse(userContextStr);
        const userContextHeader = userContext.USER_CONTEXT_HEADER;

        if (userContextHeader) {
          this.userName = userContextHeader.FIRST_NAME + ' ' + userContextHeader.LAST_NAME;

          if (userContextHeader.CURRENT_FACILITY_DESC) {
            this.facilityName = userContextHeader.CURRENT_FACILITY_DESC;
          } else if (userContextHeader.CURRENT_FACILITY) {
            this.facilityName = userContextHeader.CURRENT_FACILITY;
          }

          console.log('User context loaded from localStorage');
        }
      }
    } catch (error) {
      console.error('Error loading user context from localStorage:', error);
    }
  }

  // Public method that can be called to refresh user context immediately
  // This can be called from login pages after user context is saved
  public async refreshUserContext() {
    console.log('Refreshing user context...');
    await this.getUserContext();
  }

  // Method to get facility description from ID
  async getFacilityDescription(facilityId: string) {
    try {
      const facilityQuery = `SELECT * FROM FACILITY_HEADER WHERE FACILITY_ID = '${facilityId}'`;
      const facilityResult = await this.unviredSDK.dbExecuteStatement(facilityQuery);

      if (facilityResult.type === ResultType.success && facilityResult.data?.length > 0) {
        this.facilityName = facilityResult.data[0].DESCRIPTION || facilityId;
      } else {
        this.facilityName = facilityId; // Fallback to ID if description not found
      }
    } catch (error) {
      console.error('Error getting facility description:', error);
      this.facilityName = facilityId; // Fallback to ID on error
    }
  }

  //Make server call
  async serverCall() {
    this.dataService.isStartCustomization = false;
    // Displaying customization daialog.
    await this.displayPleaseWaitLoader();
    // Download Customization
    await this.dataService.getCustomization();
    let userAgentResult = await this.unviredSDK.dbExecuteStatement(
      `SELECT * FROM USER_CONTEXT_HEADER`
    );
    if (userAgentResult.type == ResultType.success) {
      if (userAgentResult?.data?.length > 0) {
        this.userFacility = userAgentResult.data[0].CURRENT_FACILITY;
      }
    }
    let customData = {
      STRUCTURE: [
        {
          STRUCTURE_HEADER: {
            FACILITY_ID: this.userFacility,
            DIVISION_ID: '',
            TAG: '',
            NAME: '',
            CATEGORY: '',
            STRUCT_TYPE: '',
            STATUS: '',
            P_MODE: '',
          },
        },
      ],
    };
   await this.dataService.getStructures(customData);
   let res =  await this.dataService.downloadPermits(null, null, true);
    console.log("res in home is " , res)
    if(res.type == ResultType.success){
      this.loadingController.dismiss();

      // Refresh side navigation permissions after data download
      if (this.sideNav) {
        await this.sideNav.refreshPermissions();
      }

      // Emit facility changed event to refresh UI components
      this.facilitySelectionService.emitFacilityChanged();
    }

  }

  // Display Loading dialog.
  async displayPleaseWaitLoader() {
    const loading = await this.loadingController.create({
      message: 'Please wait for data download to complete...',
      backdropDismiss: false,
    });
    await loading.present();
  }

  logoutToInitialPage() {
    // Logout logic
    localStorage.clear();
    sessionStorage.clear();
    this.router.navigate(['/login']);
  }

  // Update header/sidebar visibility based on current route
  private updateHeaderSidebarVisibility() {
    const currentUrl = this.router.url;
    console.log('Checking visibility for URL:', currentUrl);

    // Check if current route is an auth page
    const isAuthPage = this.authPages.some(page => currentUrl.startsWith(page));

    this.showHeaderAndSidebar = !isAuthPage;
    console.log('Show header/sidebar:', this.showHeaderAndSidebar, 'for URL:', currentUrl);
  }

  // Getter method for template - more reactive approach
  get shouldShowHeaderAndSidebar(): boolean {
    const currentUrl = this.router.url;
    const isAuthPage = this.authPages.some(page => currentUrl.startsWith(page));
    const shouldShow = !isAuthPage;

    console.log('Template check - URL:', currentUrl, 'Should show header/sidebar:', shouldShow);
    return shouldShow;
  }
}
