import { NgModule, Injectable } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes, CanActivate, Router } from '@angular/router';
import { InitGuard } from './guards/init.guard';
import { Platform } from '@ionic/angular';

@Injectable({
  providedIn: 'root'
})
export class PlatformRoutingGuard implements CanActivate {
  constructor(private platform: Platform, private router: Router) {}

  async canActivate(): Promise<boolean> {
    await this.platform.ready();

    // Only redirect if we're at the exact root path
    const currentUrl = this.router.url;
    if (currentUrl === '/' || currentUrl === '') {
      if (this.platform.is('android') || this.platform.is('ios')) {
        // Mobile platform - redirect to mobile-login
        this.router.navigate(['/mobile-login'], { replaceUrl: true });
      } else {
        // Browser platform - redirect to login
        this.router.navigate(['/login'], { replaceUrl: true });
      }
      return false; // Prevent the original route from activating
    }

    // For any other path, allow normal routing
    return true;
  }
}

const routes: Routes = [
  {
    path: '',
    canActivate: [PlatformRoutingGuard],
    children: []
  },
  {
    path: 'login',
    loadChildren: () =>
      import('./pages/login/login.module').then((m) => m.LoginPageModule),
  },
  {
    path: 'contractor-login',
    loadChildren: () =>
      import('./pages/contractor-login/contractor-login.module').then(
        (m) => m.ContractorLoginPageModule
      ),
  },
  {
    path: 'settings',
    loadChildren: () =>
      import('./pages/settings/settings.module').then(
        (m) => m.SettingsPageModule
      ),
  },
  {
    path: 'master-data',
    loadChildren: () =>
      import('./pages/master-data/master-data.module').then(
        (m) => m.MasterDataPageModule
      ),
  },
  {
    path: 'users',
    loadChildren: () =>
      import('./pages/users/users.module').then((m) => m.UsersPageModule),
  },
  {
    path: 'agents',
    loadChildren: () =>
      import('./pages/agents/agents.module').then((m) => m.AgentsPageModule),
  },
  {
    path: 'facilities-divisions',
    loadChildren: () =>
      import('./pages/facilities-divisions/facilities-divisions.module').then(
        (m) => m.FacilitiesDivisionsPageModule
      ),
  },
  {
    path: 'structure',
    loadChildren: () => import('./pages/structure/structure.module').then(m => m.StructurePageModule)
  },
  {
    path: 'permits',
    loadChildren: () =>
      import('./pages/permits/permits.module').then((m) => m.PermitsPageModule),
    canActivate: [InitGuard]
  },
  {
    path: 'form-render',
    loadChildren: () =>
      import('./pages/form-render/form-render.module').then(
        (m) => m.FormRenderPageModule
      ),
  },
  {
    path: 'options-popover',
    loadChildren: () =>
      import('./pages/options-popover/options-popover.module').then(
        (m) => m.OptionsPopoverPageModule
      ),
  },
  {
    path: 'camera',
    loadChildren: () =>
      import('./pages/camera/camera.module').then((m) => m.CameraPageModule),
  },
  {
    path: 'image-preview',
    loadChildren: () => import('./pages/image-preview/image-preview.module').then( m => m.ImagePreviewPageModule)
  },
  {
    path: 'reports',
    loadChildren: () => import('./pages/reports/reports.module').then( m => m.ReportsPageModule)
  },
  {
    path: 'ssologin',
    loadChildren: () => import('./pages/saml-sso/saml-sso.module').then( m => m.SamlSsoPageModule)
  },
  {
    path: 'mobile-login',
    loadChildren: () => import('./pages/mobile-login/mobile-login.module').then( m => m.MobileLoginPageModule)
  },
  {
    path: 'mobile-home',
    loadChildren: () => import('./pages/mobile-home/mobile-home.module').then( m => m.MobileHomePageModule)
  },
  {
    path: 'permissions',
    loadChildren: () => import('./pages/permissions/permissions.module').then( m => m.PermissionsPageModule)
  }
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
