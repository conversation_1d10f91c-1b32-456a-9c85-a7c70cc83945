import { Injectable, NgZone } from '@angular/core';
import { AppConstants } from '../shared/app-constants';
import { BehaviorSubject, Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import {
  DbResult,
  LoginListenerType,
  LoginParameters,
  LoginResult,
  RequestType,
  ResultType,
  SyncResult,
  UnviredCordovaSDK,
} from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import {
  AlertController,
  LoadingController,
  ModalController,
  ToastController,
} from '@ionic/angular';
import { Route, Router } from '@angular/router';
import {
  FORM_HEADER,
  PERMIT_HEADER,
  PERMIT_QUERY_CTX_HEADER,
} from '../data-models/data_classes';
import { HelperFunctionService } from './HelperFunction.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';

@Injectable({
  providedIn: 'root',
})
export class DataService {
  private _jsonURLforChatMessage = './assets/jsonData/chatMessage.json';
  public isPlatformReady: BehaviorSubject<boolean>;
  public constants: AppConstants;
  public checkUMPCallDone: BehaviorSubject<boolean>;
  public appInitializedFirstTime: BehaviorSubject<boolean>;
  public cameraOpened!: BehaviorSubject<boolean>;
  public platform: string = '';
  public url: string = '';
  public isStartCustomization: boolean = false;
  public isUserPageOpenedOnce: boolean = false;
  public loginResultType: number;
  public applicationVersion: string = '';
  public isLoading = false;
  public urlUmp: string = '';
  public selctedFacilityId = "";
  public internalUsersArray: any[] = [];
  public externalUsersArray: any[] = [];
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;
  public isRevised : boolean = false;

  constructor(
    private http: HttpClient,
    private unviredSDK: UnviredCordovaSDK,
    public toastController: ToastController,
    public ngZone: NgZone,
    private route: Router,
    public alertController: AlertController,
    public loadingController: LoadingController,
    public modalCtrl: ModalController,
    private httpClient: HttpClient,
    private utilityFunction: HelperFunctionService,
    private device: Device,
    private configService: ConfigService,
  ) {
    this.constants = new AppConstants();
    this.checkUMPCallDone = new BehaviorSubject(false);
    this.appInitializedFirstTime = new BehaviorSubject(false);
    this.isPlatformReady = new BehaviorSubject(false);
  }

  // Get platform status when its changes
  getPlatformStatus(): Observable<boolean> {
    return this.isPlatformReady.asObservable();
  }

  // Get Status for UMP calls
  getServerCallStatus(): Observable<boolean> {
    return this.checkUMPCallDone.asObservable();
  }

  // Get app initialized very first time
  getAppInitializedStatus(): Observable<boolean> {
    return this.appInitializedFirstTime.asObservable();
  }
  openCameraEvent(): Observable<boolean> {
    return this.cameraOpened.asObservable();
  }

  setDevicePlatform(platformName: string) {
    this.platform = platformName;
  }

  getDevicePlatform() {
    this.platform = this.device.platform;
    return this.platform;
  }

  // Toast for showing notifications
  async showToast(msg: string) {
    const toast = await this.toastController.create({
      message: msg,
      animated: true,
      color: 'success',
      duration: 3000,
      position: 'top',
      cssClass: 'success-toast',
      buttons: [
        {
          text: 'OK',
          role: 'cancel'
        }
      ]
    });
    toast.present();
  }

  getUmpUrl(url = null) {
    // If no URL is supplied, read the URL from the address bar.
    if (!url) {
      url = location.href;
    }

    let urlPath = '';
    if (
      url.indexOf('localhost') == -1 &&
      url.indexOf('127.0.0.1') == -1 &&
      url.toLowerCase().indexOf('/permits') > -1
    ) {
      urlPath = url
        .toLowerCase()
        .substring(0, url.toLowerCase().indexOf('/permits'));
      /**
       * We append /UMP at the end to make the fully functional URL for downloading and uploading attachment.
       */
      urlPath += '/UMP';
      console.log('getUmpurl function URL  :  ' + urlPath);
    } else {
      // Use config.json umpUrl if available, otherwise fall back to constants
      urlPath = this.configService.getUmpUrl() || this.constants.LOGIN_PARAMS.URL;
      console.log(
        'urlPath from the login_params in getUmpurl function:  ' + urlPath
      );
    }
    // remove last index of / from url
    if (urlPath && urlPath.length > 0) {
      if (urlPath.slice(-1) == '/') {
        urlPath = urlPath.slice(0, -1) + '';
      }
    }
    console.log('return ump url', urlPath)
    return urlPath;
  }

  // Get application version in production
  getAppVersion() {
    return (this.applicationVersion =
      this.constants.APP_RELEASE_NUMBER +
      ' - ' +
      this.constants.APP_RELEASE_DATE);
  }

  async getUserContext() {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.PULL,
        '',
        '',
        AppConstants.PA_GET_USER_CONTEXT,
        true
      );
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getUserContext',
        'Error while downloading User Context : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Initalize login process for different mode of login and activation
  async initializeLoginProcess() {
    alert(`initializeLoginProcess called with platform: ${this.platform}`);
    try {
      let loginParameters = new LoginParameters();
      loginParameters.appName = this.constants.LOGIN_PARAMS.APP_NAME;
      loginParameters.metadataPath = this.constants.LOGIN_PARAMS.METADATA_PATH;
      loginParameters.autoSyncTime = '30';
      loginParameters.cacheWebData = true;
      loginParameters.url = this.getUmpUrl();
      loginParameters['appVersion'] = this.constants.APP_RELEASE_NUMBER;
      let loginResult: LoginResult = null;
      alert('About to call unviredSDK.login()');
      loginResult = await this.unviredSDK.login(loginParameters);
      alert(`Login completed with result: ${loginResult ? loginResult.type : 'null'}`);
      this.loginResultType = loginResult.type;



      alert(`Login result type: ${loginResult.type}`);
      console.log('Data service initializeLoginProcess - loginResult.type:', loginResult.type);
      console.log('Data service platform detection:', {
        platform: this.platform,
        devicePlatform: this.device.platform
      });

      switch (loginResult.type) {
        case LoginListenerType.auth_activation_required:
          // Display Login Screen |auth_activation_required|
          console.log('auth_activation_required - platform check:', this.platform);
          if (this.platform === 'browser' || this.device.platform === 'browser') {
            console.log('Navigating to /login for browser');
            this.ngZone.run(() => {
              this.route.navigateByUrl('/login', { replaceUrl: true });
            });
          } else {
            console.log('Navigating to /mobile-login for mobile platform:', this.platform);
            this.ngZone.run(() => {
              this.route.navigateByUrl('/mobile-login', { replaceUrl: true });
            });
          }
          break;
        case LoginListenerType.app_requires_login:
          // Display Login Screen |app_requires_login|
          console.log('app_requires_login - platform check:', this.platform);
          if (this.platform === 'browser' || this.device.platform === 'browser') {
            console.log('Navigating to /login for browser');
            this.ngZone.run(() => {
              this.route.navigateByUrl('/login'), { replaceUrl: true };
            });
          } else {
            console.log('Navigating to /mobile-login for mobile platform:', this.platform);
            this.ngZone.run(() => {
              this.route.navigateByUrl('/mobile-login', { replaceUrl: true });
            });
          }
          break;
        case LoginListenerType.login_success:
          // Srinidhi: 4 Oct 2022: Set this flag so that when the users navigate to the home page, then it starts downloading customization data.
          this.isStartCustomization = true;
          this.displayLandingPage();
          break;
      }
    } catch (error) {
      alert(`Error in initializeLoginProcess: ${error}`);
      this.unviredSDK.logError(
        'data service',
        'initializeLoginProcces()',
        'ERROR: ' + error
      );
    }
  }

  //Adding logout functionality to initial page
  async logoutToInitialPage() {
    // Check if we're already on a login page to prevent infinite redirects
    const currentUrl = this.route.url;
    if (currentUrl.includes('/login') || currentUrl.includes('/contractor-login') || currentUrl.includes('/mobile-login')) {
      console.log('Already on login page, skipping logout redirect');
      return;
    }

    let logoutResult;
    try {
      logoutResult = await this.unviredSDK.logout();
    } catch (error) {
      console.log('Error while logging out: ' + JSON.stringify(error));
    } finally {
      // Clear local storage.
      localStorage.clear();

      console.log(
        `************Successfully logout to initial page********** ${JSON.stringify(
          logoutResult
        )}`
      );
      this.initializeLoginProcess();
    }
  }

  displayLandingPage() {
    if (this.device.platform === 'browser') {
      this.ngZone.run(() => {
        this.route.navigateByUrl('/permits');
      });
    } else {
      this.ngZone.run(() => {
        this.route.navigateByUrl('/mobile-home', { replaceUrl: true });
      });
    }
  }

  // Get Customization
  async getCustomization() {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.PULL,
        '',
        '',
        AppConstants.PA_GET_CUSTOMIZATION,
        true
      );
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'getCustomization',
          'Customization has downloaded successfully.'
        );
        await this.unviredSDK.dbSaveWebData();
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'getCustomization',
          'Error while downloading Customization.'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getCustomization',
          'Error while downloading Customization : ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getCustomization',
        'Error while downloading Customization : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Get Data from Local DB
  async getData(
    headerName: string,
    whereCondition?: string,
    orderByClause?: string
  ) {
    var dbStatement = '';
    if (whereCondition) {
      dbStatement += `SELECT * FROM ${headerName} WHERE ${whereCondition}`;
    } else {
      dbStatement += `SELECT * FROM ${headerName}`;
    }

    if (orderByClause) {
      dbStatement += ` ORDER BY ${orderByClause}`;
    }

    var result = await this.unviredSDK.dbExecuteStatement(dbStatement);

    if (result.type == ResultType.success) {
      return result.data;
    } else {
      this.unviredSDK.logError(
        'dataService',
        'getData',
        `Error while fetching ${headerName} Data - ${result.message}`
      );
    }
  }

  // Get Facility
  async getFacility() {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.PULL,
        '',
        '',
        AppConstants.PA_GET_FACILITY,
        true
      );
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'getFacility',
          'Facilities has downloaded successfully.'
        );
        await this.unviredSDK.dbSaveWebData();
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'getFacility',
          'Error while downloading Facilities.'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getFacility',
          'Error while downloading Facilities : ' + result.message
        );
      }
      return result.data;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getFacility',
        'Error while downloading Facilities : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  async exportDb() {
    await this.unviredSDK.dbExportWebData();
  }

  // Error/Warning Alert for user
  async showAlertMessage(msgType: string, msg: string) {
    if (msgType === 'Error') {
      msgType = 'Error';
    } else if (msgType === 'Warning') {
      msgType = 'Warning';
    }
    const e_alert = await this.alertController.create({
      header: msgType,
      message: msg,
      animated: true,
      backdropDismiss: false,
      buttons: ['OK'],
    });
    await e_alert.present();
  }

  async showBusyIndicator(msg: string, indType: string) {
    let loader = null;
    this.isLoading = true;
    if (indType == 'dots') {
      loader = await this.loadingController.create({
        message: msg,
        spinner: 'dots',
        animated: true,
        showBackdrop: true,
        translucent: true,
      });
    } else {
      loader = await this.loadingController.create({
        message: msg,
        spinner: 'crescent',
        animated: true,
        showBackdrop: true,
        translucent: true,
      });
    }
    await loader.present();
    if (!this.isLoading) {
      this.loadingController.dismiss();
    }
  }

  async dismissBusyIndicator() {
    this.isLoading = false;
    return await this.loadingController.dismiss();
  }

  async displayErrorMessageDialog(msg: string) {
    const alert = await this.alertController.create({
      header: 'Error',
      message: msg,
      buttons: ['OK'],
    });

    await alert.present();
  }

  // Get Structure Types
  async getStructureTypes() {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.PULL,
        '',
        '',
        AppConstants.PA_GET_STRUCTURE_TYPE,
        true
      );
      if (result.type == ResultType.success) {
        await this.unviredSDK.dbSaveWebData();
        this.unviredSDK.logInfo(
          'DataService',
          'getStructureTypes',
          'Structure Types has downloaded successfully.'
        );
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'getStructureTypes',
          'Error while downloading Structure Types.'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getStructureTypes',
          'Error while downloading Structure Types : ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getStructureTypes',
        'Error while downloading Structure Types : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Get Structure Types from Db
  async getStructureType() {
    let result = await this.unviredSDK.dbExecuteStatement(
      'SELECT * FROM STRUCTURE_TYPE_HEADER ORDER BY CAST(STRUCT_TYPE AS NUMERIC) ASC;'
    );
    return result;
  }

  // Get Structure Categories
  async getStructureCategories() {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.PULL,
        '',
        '',
        AppConstants.PA_GET_STRUCTURE_CATEGORY,
        true
      );
      if (result.type == ResultType.success) {
        await this.unviredSDK.dbSaveWebData();
        this.unviredSDK.logInfo(
          'DataService',
          'getStructureCategories',
          'Structure Categories has downloaded successfully.'
        );
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'getStructureCategories',
          'Error while downloading Structure Categories.'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getStructureCategories',
          'Error while downloading Structure Categories : ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getStructureCategories',
        'Error while downloading Structure Categories : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Get Structure Categories from db
  async getStructureCategorie() {
    let result = await this.unviredSDK.dbExecuteStatement(
      'SELECT * FROM STRUCTURE_CAT_HEADER'
    );
    return result;
  }

  // Get Structure Status
  async getStructureStatus() {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.PULL,
        '',
        '',
        AppConstants.PA_GET_STRUCTURE_STATUS,
        true
      );
      if (result.type == ResultType.success) {
        await this.unviredSDK.dbSaveWebData();
        this.unviredSDK.logInfo(
          'DataService',
          'getStructureStatus',
          'Structure Status has downloaded successfully.'
        );
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'getStructureStatus',
          'Error while downloading Structure Status.'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getStructureStatus',
          'Error while downloading Structure Status : ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getStructureStatus',
        'Error while downloading Structure Status : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Get Structure Status from DB
  async getStructrStatus() {
    let result = await this.unviredSDK.dbExecuteStatement(
      'SELECT * FROM STRUCTURE_STATUS_HEADER'
    );
    return result;
  }

  // Get User Roles
  async getRoles() {
    // try {
    //   let result = await this.unviredSDK.syncForeground(
    //     RequestType.PULL,
    //     '',
    //     '',
    //     AppConstants.PA_GET_ROLE,
    //     true
    //   );
    //   if (result.type == ResultType.success) {
    //     this.unviredSDK.logInfo(
    //       'DataService',
    //       'getUserRoles',
    //       'user roles has downloaded successfully.'
    //     );
    //     await this.unviredSDK.dbSaveWebData();
    //   } else if (result.code && result.code === 401) {
    //     await this.logoutToInitialPage();
    //     this.unviredSDK.logError(
    //       'DataService',
    //       'getUserRoles',
    //       'Error while downloading user roles.'
    //     );
    //   } else {
    //     this.unviredSDK.logError(
    //       'DataService',
    //       'getUserRoles',
    //       'Error while downloading user roles : ' + result.message
    //     );
    //   }
    //   return result.data;
    // } catch (error) {
    //   this.unviredSDK.logError(
    //     'DataService',
    //     'getUserRoles',
    //     'Error while downloading user roles : ' + error
    //   );
    //   let err = { type: ResultType.error, error: error };
    //   return err;
    // }
  }

  // Modify Structure Types
  async modifyStructureType(inputHeader: any) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.RQST,
        '',
        inputHeader,
        AppConstants.PA_MODIFY_STRUCTURE_TYPE,
        true
      );
      if (result.type == ResultType.success) {
        await this.unviredSDK.dbSaveWebData();
        this.unviredSDK.logInfo(
          'DataService',
          'modifyStructureType',
          'Structure Types has modified successfully.'
        );
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'modifyStructureType',
          'Error in structure types modification'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'modifyStructureType',
          'ERROR: ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'modifyStructureType',
        'ERROR: ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  async modifyApprovalType(inputHeader: any) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.RQST,
        '',
        inputHeader,
        AppConstants.PA_MODIFY_APPROVAL_TYPE,
        true
      );
      if (result.type == ResultType.success) {
        await this.unviredSDK.dbSaveWebData();
        this.unviredSDK.logInfo(
          'DataService',
          'modifyStructureType',
          'Structure Types has modified successfully.'
        );
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'modifyStructureType',
          'Error in structure types modification'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'modifyStructureType',
          'ERROR: ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'modifyStructureType',
        'ERROR: ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  async modifyPermitType(inputHeader: any) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.RQST,
        '',
        inputHeader,
        AppConstants.PA_MODIFY_PERMIT_TYPE,
        true
      );
      if (result.type == ResultType.success) {
        await this.unviredSDK.dbSaveWebData();
        this.unviredSDK.logInfo(
          'DataService',
          'modifyPermitType',
          'Permit Type has modified successfully.'
        );
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'modifyPermitType',
          'Error inPermit Type modification'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'modifyPermitType',
          'ERROR: ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'modifyPermitType',
        'ERROR: ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Modify Structure Categories
  async modifyStructureCategories(inputHeader: any) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.RQST,
        '',
        inputHeader,
        AppConstants.PA_MODIFY_STRUCTURE_CATEGORY,
        true
      );
      if (result.type == ResultType.success) {
        await this.unviredSDK.dbSaveWebData();
        this.unviredSDK.logInfo(
          'DataService',
          'modifyStructureCategories',
          'Structure Categories has modified successfully.'
        );
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'modifyStructureCategories',
          'Error in structure Categories modification'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'modifyStructureCategories',
          'ERROR: ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'modifyStructureCategories',
        'ERROR: ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Modify Structure Status
  async modifyStructureStatus(inputHeader: any) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.RQST,
        '',
        inputHeader,
        AppConstants.PA_MODIFY_STRUCTURE_STATUS,
        true
      );
      if (result.type == ResultType.success) {
        await this.unviredSDK.dbSaveWebData();
        this.unviredSDK.logInfo(
          'DataService',
          'modifyStructureStatus',
          'Structure Status has modified successfully.'
        );
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'modifyStructureStatus',
          'Error in structure Status modification'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'modifyStructureStatus',
          'ERROR: ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'modifyStructureStatus',
        'ERROR: ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Update/ Add Roles
  async modifyRoles(inputHeader: any) {
    console.log(inputHeader);
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.RQST,
        '',
        inputHeader,
        AppConstants.PA_MODIFY_ROLE,
        true
      );
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'updateProcedureTemplates',
          'Updated Roles.'
        );
 
        await this.unviredSDK.dbSaveWebData();
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'updateProcedureTemplates',
          'Error while Updated Roles.'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'updateProcedureTemplates',
          'Error while Updated Roles : ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'updateProcedureTemplates',
        'Error while Updated Roles : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }


  async getSkill(customData) {
    console.log("customdata in get agent user " , customData)
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.QUERY,
        '',
        customData,
        AppConstants.PA_GET_SKLL,
        true
      );
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'GetSkill',
          'user skills has downloaded successfully.'
        );
        await this.unviredSDK.dbSaveWebData();
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'GetSkill',
          'Error while downloading user skills'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'GetSkill',
          'Error while downloading user skills Details : ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'GetSkill',
        'Error while downloading user skills Details : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }


  async modifySkillType(inputHeader: any){
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.RQST,
        '',
        inputHeader,
        AppConstants.PA_MODIFY_SKILL,
        true
      );
      if (result.type == ResultType.success) {
        await this.unviredSDK.dbSaveWebData();
        this.unviredSDK.logInfo(
          'DataService',
          'modifySkillType',
          'Skill Types has modified successfully.'
        );
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'modifySkillType',
          'Error in skill types modification'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'modifySkillType',
          'ERROR: ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'modifySkillType',
        'ERROR: ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  sleep(ms: any) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async executeQuery(query: string) {
    var result = await this.unviredSDK.dbExecuteStatement(query);
    if (result.type == ResultType.success) {
      return result.data;
    } else {
      this.unviredSDK.logError(
        'dataService',
        'executeQuery',
        `Error while Executing Query - ${result.message}`
      );
    }
  }

  // Get Agent user Details
  async getAgentUser(customData) {
    console.log("customdata in get agent user " , customData)
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.QUERY,
        '',
        customData,
        AppConstants.PA_GET_AGENT_USER,
        true
      );
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'AgentUser',
          'user Details has downloaded successfully.'
        );
        await this.unviredSDK.dbSaveWebData();
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'AgentUser',
          'Error while downloading Agent User Details.'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'AgentUser',
          'Error while downloading Agent User Details : ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'AgentUser',
        'Error while downloading Agent User Details : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  async getUserApprovalsByUserIdgetAgentUser(customData) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.PULL,
        '',
        customData,
        AppConstants.PA_GET_USER_APPROVAL_TYPE,
        true
      );
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'getUserApprovalsByUserIdgetAgentUser',
          'user Details has downloaded successfully.'
        );
        await this.unviredSDK.dbSaveWebData();
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'getUserApprovalsByUserIdgetAgentUser',
          'Error while downloading Approvals Details.'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getUserApprovalsByUserIdgetAgentUser',
          'Error while downloading Approvals Details : ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getUserApprovalsByUserIdgetAgentUser',
        'Error while downloading Approvals Details : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Update Agent user Header
  async createAgentUsers(customdata) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.QUERY,
        '',
        customdata,
        AppConstants.PA_CREATE_USER,
        true
      );
      if (result) {
        if (result.type == ResultType.success) {
          await this.unviredSDK.dbSaveWebData();
          if (result.data.InfoMessage === undefined) {
            this.unviredSDK.logInfo(
              'DataService',
              'createAgentUsers',
              'Agent User has been added successfully.'
            );
            this.modalCtrl.dismiss('Agent User Added Successfully');
          } else if (result.data.InfoMessage.length > 0) {
            return result.message.trim();
          }
        }
        if (result.type == ResultType.error) {
    
          if (result.code && result.code === 401) {
            this.unviredSDK.logError(
              'DataService',
              'createAgentUsers',
              'Error while adding Agent User.'
            );
            this.modalCtrl.dismiss();
            this.logoutToInitialPage();
          } else if (result.data.InfoMessage.length > 0) {

            return result;
          }
        }
      } else {
        this.unviredSDK.logError(
          'DataService',
          'createAgentUsers',
          'Error while adding Agent User : ' + result.message
        );
        return 'Error Adding Agent User';
      }
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'createAgentUsers',
        'Error while adding Agent users : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  //sending a mail for user to change password
  sendResetMail(username): Observable<any> {
    // Use config.json domain if available, otherwise fall back to constants
    let company = this.configService.getDomain() || this.constants.LOGIN_PARAMS.COMPANY;
    let currentUrl = location.href;
    this.url = this.getUmpUrl(currentUrl);
    this.urlUmp = this.url;
    if (!this.urlUmp.endsWith(`/UMP`) && !this.urlUmp.endsWith(`/UMP/`)) {
      this.urlUmp = this.urlUmp.concat('/UMP');
    }
    if (!this.urlUmp.endsWith('/')) {
      this.urlUmp = this.urlUmp.concat('/');
    }
    console.log('URL from sending reset email: ' + this.urlUmp);

    let apiUrl = `${this.urlUmp}API/v2/companies/${company}/users/${username}/forgotpassword?mailTemplate=FORGOT_PASSWORD_TEMPLATE&application=PERMIT`;
    console.log({ company, currentUrl, username, apiUrl });
    return this.httpClient.get(apiUrl, { observe: 'response' });
  }

  // Update Agent user Details
  async modifyUserDetails(customdata, isUpdatingContent) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.QUERY,
        '',
        customdata,
        AppConstants.PA_MODIFY_USER,
        true
      );

      if (result) {
        if (result.type == ResultType.success) {
          await this.unviredSDK.dbSaveWebData();
          if (result.data.InfoMessage === undefined) {
            this.unviredSDK.logInfo(
              'DataService',
              'modifyUserDetails',
              'User has been Modified successfully.'
            );
            // Show toast notification instead of alert
            if (isUpdatingContent) {
              this.showToast('User Updated Successfully');
            } else {
              this.showToast('User Added Successfully');
            }
            this.modalCtrl.dismiss({ status: true });
            return result;
          } else if (result.data.InfoMessage.length > 0) {
            return result.message.trim();
          }
        }
        if (result.type == ResultType.error) {
          if (result.code && result.code === 401) {
            this.unviredSDK.logError(
              'DataService',
              'modifyUserDetails',
              'Error while adding User.'
            );
            this.modalCtrl.dismiss();
            this.logoutToInitialPage();
          } else if (result.data.InfoMessage.length > 0) {
            return result.message.trim();
          }
        }
      } else {
        this.unviredSDK.logError(
          'DataService',
          'modifyUserDetails',
          'Error while adding User : ' + result.message
        );
        return 'Error Adding User';
      }
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'modifyUserDetails',
        'Error while modifying Agent User Details : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }


  async modifyUserDetailsRequest(customdata, isUpdatingContent) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.RQST,
        customdata,
        '',
        AppConstants.PA_MODIFY_USER,
        true
      );

      if (result) {
        if (result.type == ResultType.success) {
          // await this.unviredSDK.dbSaveWebData();
          if (result.data.InfoMessage === undefined) {
            this.unviredSDK.logInfo(
              'DataService',
              'modifyUserDetails',
              'User has been Modified successfully.'
            );
            // Show toast notification instead of alert
            if (isUpdatingContent) {
              this.showToast('User Updated Successfully');
            } else {
              this.showToast('User Added Successfully');
            }
            this.modalCtrl.dismiss({ status: true });
            return result;
          } else if (result.data.InfoMessage.length > 0) {
            return result.message.trim();
          }
        }
        if (result.type == ResultType.error) {
          if (result.code && result.code === 401) {
            this.unviredSDK.logError(
              'DataService',
              'modifyUserDetails',
              'Error while adding User.'
            );
            this.modalCtrl.dismiss();
            this.logoutToInitialPage();
          } else if (result.data.InfoMessage.length > 0) {
            return result.message.trim();
          }
        }
      } else {
        this.unviredSDK.logError(
          'DataService',
          'modifyUserDetails',
          'Error while adding User : ' + result.message
        );
        return 'Error Adding User';
      }
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'modifyUserDetails',
        'Error while modifying Agent User Details : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Update Agent user Details
  async modifyUserApprovals(customdata) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.QUERY,
        '',
        customdata,
        AppConstants.PA_MODIFY_USER_APPROVAL_TYPE,
        true
      );
      if (result) {
        if (result.type == ResultType.success) {
          await this.unviredSDK.dbSaveWebData();
          if (result.data.InfoMessage === undefined) {
            this.unviredSDK.logInfo(
              'DataService',
              'modifyUserDetails',
              'User approvals has been Modified successfully.'
            );
          } else if (result.data.InfoMessage.length > 0) {
            return result.message.trim();
          } else return result;
          return result;
        }
        if (result.type == ResultType.error) {
          if (result.code && result.code === 401) {
            this.unviredSDK.logError(
              'DataService',
              'modifyUserDetails',
              'Error while adding User Approvals.'
            );
            this.modalCtrl.dismiss();
            this.logoutToInitialPage();
          } else if (result.data.InfoMessage.length > 0) {
            return result.message.trim();
          }
        }
      } else {
        this.unviredSDK.logError(
          'DataService',
          'modifyUserDetails',
          'Error while adding User Approvals : ' + result.message
        );
        return 'Error Adding User Approvals';
      }
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'modifyUserDetails',
        'Error while modifying User Approvals : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Get Agens Data
  async getAgents() {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.PULL,
        '',
        '',
        AppConstants.PA_GET_AGENT,
        true
      );
      if (result.type == ResultType.success) {
        await this.unviredSDK.dbSaveWebData();
        this.unviredSDK.logInfo(
          'DataService',
          'getAgents',
          'Agents Data has downloaded successfully.'
        );
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'getAgents',
          'Error while downloading Agents Data.'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getAgents',
          'Error while downloading Agents Data : ' + result.message
        );
      }
      return result.data;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getAgents',
        'Error while downloading Agents Data : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Modify Agent Details
  async modifyAgent(header, isUpdatingContent) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.RQST,
        header,
        '',
        AppConstants.PA_MODIFY_AGENT,
        true
      );
      if (result) {
        if (result.type == ResultType.success) {
          await this.unviredSDK.dbSaveWebData();
          if (result.data.InfoMessage === undefined) {
            this.unviredSDK.logInfo(
              'DataService',
              'modifyAgent',
              'user context has been modified successfully.'
            );
            // Show toast notification instead of alert
            this.showToast(
              isUpdatingContent
                ? 'Agent Updated Successfully'
                : 'Agent Added Successfully'
            );
            // Dismiss modal without data to prevent alert
            this.modalCtrl.dismiss();
          } else if (result.data.InfoMessage.length > 0) {
            return result.message.trim();
          }
        }
        if (result.type == ResultType.error) {
          if (result.code && result.code === 401) {
            this.unviredSDK.logError(
              'DataService',
              'modifyAgent',
              'Error while modifying UserContext.'
            );
            this.modalCtrl.dismiss();
            this.logoutToInitialPage();
          } else if (result.data.InfoMessage.length > 0) {
            return result.message.trim();
          }
        }
      } else {
        this.unviredSDK.logError(
          'DataService',
          'modifyAgent',
          'Error while modifying UserContext : ' + result.message
        );
        return isUpdatingContent
          ? 'Error Updating Agent'
          : 'Error Adding Agent';
      }
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'modifyAgent',
        'Error while updating Agent Details : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Get Divisions
  async getDivisions(customData) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.QUERY,
        '',
        customData,
        AppConstants.PA_GET_DIVISION,
        true
      );
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'getDivisions',
          'Divisions has downloaded successfully.'
        );
        await this.unviredSDK.dbSaveWebData();
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'getDivisions',
          'Error while downloading Divisions.'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getDivisions',
          'Error while downloading Divisions : ' + result.message
        );
      }
      return result.data;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getDivisions',
        'Error while downloading Divisions : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Get structures
  async getStructures(customData) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.QUERY,
        '',
        customData,
        AppConstants.PA_GET_STRUCTURE,
        true
      );
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'getStructures',
          'structures has downloaded successfully.'
        );
        await this.unviredSDK.dbSaveWebData();
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'getStructures',
          'Error while downloading structures.'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getStructures',
          'Error while downloading structures : ' + result.message
        );
      }
      return result.data;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getStructures',
        'Error while downloading structures : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Add Facility
  async addFacility(customData) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.QUERY,
        '',
        customData,
        AppConstants.PA_MODIFY_FACILITY,
        true
      );
      if (result) {
        if (result.type == ResultType.success) {
          await this.unviredSDK.dbSaveWebData();
          if (result.data.InfoMessage === undefined) {
            this.unviredSDK.logInfo(
              'DataService',
              'addFacility',
              'Facility has been added successfully.'
            );
            this.modalCtrl.dismiss('Facility Added Successfully');
          } else if (result.data.InfoMessage.length > 0) {
            return result.message.trim();
          }
        }
        if (result.type == ResultType.error) {
          if (result.code && result.code === 401) {
            this.unviredSDK.logError(
              'DataService',
              'addFacility',
              'Error while adding Facility.'
            );
            this.modalCtrl.dismiss();
            this.logoutToInitialPage();
          } else if (result.data.InfoMessage.length > 0) {
            return result.message.trim();
          }
        }
      } else {
        this.unviredSDK.logError(
          'DataService',
          'addFacility',
          'Error while adding Facility : ' + result.message
        );
        return 'Error Adding Facility';
      }
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'addFacility',
        'Error while adding Facility : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Add Division
  async addDivision(customData) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.QUERY,
        '',
        customData,
        AppConstants.PA_MODIFY_DIVISION,
        true
      );
      if (result) {
        if (result.type == ResultType.success) {
          await this.unviredSDK.dbSaveWebData();
          if (result.data.InfoMessage === undefined) {
            this.unviredSDK.logInfo(
              'DataService',
              'addDivision',
              'Division has been added successfully.'
            );
            this.modalCtrl.dismiss('Division Added Successfully');
          } else if (result.data.InfoMessage.length > 0) {
            return result.message.trim();
          }
        }
        if (result.type == ResultType.error) {
          if (result.code && result.code === 401) {
            this.unviredSDK.logError(
              'DataService',
              'addDivision',
              'Error while adding Division.'
            );
            this.modalCtrl.dismiss();
            this.logoutToInitialPage();
          } else if (result.data.InfoMessage.length > 0) {
            return result.message.trim();
          }
        }
      } else {
        this.unviredSDK.logError(
          'DataService',
          'addDivision',
          'Error while adding Division : ' + result.message
        );
        return 'Error Adding Division';
      }
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'addDivision',
        'Error while adding Division : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Add Bulk Structures
  async addBulkStructures(customdata) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.QUERY,
        '',
        customdata,
        AppConstants.PA_BULK_UPLOAD_STRUCTURE,
        true
      );
      if (result) {
        return result;
      } else {
        this.unviredSDK.logError(
          'DataService',
          'createAgentUsers',
          'Error while adding Agent User : ' + result.message
        );
        return 'Error Adding Agent User';
      }
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'createAgentUsers',
        'Error while adding Agent users : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Modify  user context
  async modifyUserContext(header, isUpdatingContent) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.RQST,
        { USER_CONTEXT_HEADER: header },
        '',
        AppConstants.PA_MODIFY_USER_CONTEXT,
        true
      );
      if (result) {
        if (result.type == ResultType.success) {
          await this.unviredSDK.dbSaveWebData();
          if (result.data.InfoMessage === undefined) {
            this.unviredSDK.logInfo(
              'DataService',
              'modifyUserContext',
              'user context has been modified successfully.'
            );
            this.modalCtrl.dismiss(
              isUpdatingContent
                ? 'Facility Updated Successfully'
                : 'Facility Added Successfully'
            );
          } else if (result.data.InfoMessage.length > 0) {
            return result.message.trim();
          }
        }
        if (result.type == ResultType.error) {
          if (result.code && result.code === 401) {
            this.unviredSDK.logError(
              'DataService',
              'modifyUserContext',
              'Error while modifying UserContext.'
            );
            this.modalCtrl.dismiss();
            this.logoutToInitialPage();
          } else if (result.data.InfoMessage.length > 0) {
            return result.message.trim();
          }
        }
      } else {
        this.unviredSDK.logError(
          'DataService',
          'modifyUserContext',
          'Error while modifying UserContext : ' + result.message
        );
        return isUpdatingContent
          ? 'Error Updating Facility'
          : 'Error Adding Facility';
      }
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'modifyUserContext',
        'Error while downloading modifyUserContext : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  handleInfoMessage(result) {
    let infoMessage = '';
    if (
      result &&
      result.data &&
      result.data.InfoMessage &&
      result.data.InfoMessage.length > 0
    ) {
      for (var info of result.data.InfoMessage) {
        if (info.category != 'SUCCESS') {
          if (infoMessage) infoMessage = infoMessage + '\n';
          infoMessage = infoMessage + info.message;
        }
      }
    }
    return infoMessage;
  }

  // Download permits from server.
  async downloadPermits(limit?: any, offset?: any, autoSave?: boolean) {
    let permitHeader = new PERMIT_QUERY_CTX_HEADER();
    permitHeader.FACILITY_ID = '';
    // Fetch agent id from user agent table.
    let userAgentResult = await this.unviredSDK.dbExecuteStatement(
      `SELECT * FROM USER_CONTEXT_HEADER`
    );
    if (userAgentResult.type == ResultType.success) {
      if (userAgentResult.data && userAgentResult.data.length > 0) {
        permitHeader.FACILITY_ID = userAgentResult.data[0].CURRENT_FACILITY;
      }
    }
    // permitHeader.REC_LIMIT = limit;
    // permitHeader.REC_OFFSET = offset;
    let inspectionHeaderInput = {
      PERMIT_QUERY_CTX: [{ PERMIT_QUERY_CTX_HEADER: permitHeader }],
    };
    // await this.unviredSDK.dbExportWebData();
    let getPermitsResponse: any = await this.getpermits(
      inspectionHeaderInput,
      RequestType.QUERY,
      autoSave
    );
    console.log("getPermitsResponse" ,getPermitsResponse)
    if (getPermitsResponse.type == ResultType.success) {
      return {
        type: ResultType.success,
        error: '',
        data: getPermitsResponse?.data,
      };
    } else {
      return { type: ResultType.error, error: getPermitsResponse.message };
    }
  }

  // Get All Permits
  async getpermits(
    inputHeader: any,
    requestType: RequestType,
    autoSave: boolean
  ) {
    try {
      let result = await this.unviredSDK.syncForeground(
        requestType,
        '',
        inputHeader,
        AppConstants.PA_GET_PERMIT,
        autoSave
      );
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'getpermits',
          'Permit has downloaded successfully.'
        );
        await this.unviredSDK.dbSaveWebData();
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'getpermits',
          'Error while downloading Permits.'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getpermits',
          'Error while downloading Permits : ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getpermits',
        'Error while downloading Permits : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  async modifyPermit(permitHeader: PERMIT_HEADER) {
    let permitHeaderInput = {
      PERMIT_HEADER: permitHeader,
    };

    try {
      let result: any;
      if (this.platform === 'browser') {
        // For web app its sync call
        result = await this.unviredSDK.syncForeground(
          RequestType.RQST,
          permitHeaderInput,
          '',
          AppConstants.PA_MODIFY_PERMIT,
          true
        );
      } else {
        // For mobile app its async call
        result = await this.unviredSDK.syncBackground(
          RequestType.RQST,
          permitHeaderInput,
          '',
          AppConstants.PA_MODIFY_PERMIT,
          'PERMIT',
          permitHeader.LID,
          true
        );
      }
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'modifyPermit',
          'Permit Header has modified successfully..'
        );
        return result;
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'modifyPermit',
          'Error while modifying Permit Header.'
        );
        return result;
      } else {
        this.unviredSDK.logError(
          'DataService',
          'modifyPermit',
          'Error while modifying Permit Header : ' + result.message
        );
        return result;
      }
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'modifyPermit',
        'Error while modifying Permit Header : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  async getFormTemplates() {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.QUERY,
        '',
        '',
        AppConstants.PA_GET_FORM_TEMPLATES,
        true
      );
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'getFormTemplates',
          'form templates has downloaded successfully.'
        );
        await this.unviredSDK.dbSaveWebData();
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'getFormTemplates',
          'Error while downloading form templates.'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getFormTemplates',
          'Error while downloading form templates : ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getFormTemplates',
        'Error while downloading form templates : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }
  async getFormDetails(permitNo, formId) {
    let dbStatement = `SELECT * FROM ${this.constants.PERMIT_FORM} WHERE PERMIT_NO = '${permitNo}' AND FORM_ID = '${formId}'`;
    var result = await this.unviredSDK.dbExecuteStatement(dbStatement);
    return result;
    // if (result.type == ResultType.success) {
    //   return result.data;
    // } else {
    //   this.unviredSDK.logError(
    //     'dataService',
    //     'getData',
    //     `Error while fetching form Data - ${result.message}`
    //   );
    // }
  }
  async updatePermiform(subData, permitNo, formId) {
    var result = await this.unviredSDK.dbUpdate(
      this.constants.PERMIT_FORM,
      subData,
      `PERMIT_NO = '${permitNo}' AND FORM_ID = '${formId}'`
    );
    return result;
  }
  async getNestedformDetailsFromDB(selNestedFormVersion, selectedNestedFormId) {
    let formDesign = '';
    let str = selectedNestedFormId + selNestedFormVersion;
    let queryToFetchForm = `SELECT TEMPLATE FROM ${this.constants.FORM_TABLE} WHERE (FORM_ID || VERSION) IN ('${str}') AND FORM_TYPE = 'N'`;

    let fetchForm: DbResult = await this.unviredSDK.dbExecuteStatement(
      queryToFetchForm
    );
    if (fetchForm.type === ResultType.success) {
      let formDB: FORM_HEADER[] = fetchForm.data;
      let formHeader: FORM_HEADER = formDB[0];
      if (formHeader) {
        formDesign = this.utilityFunction.decodeUnicode(formHeader.TEMPLATE);
      }
      let data = { type: ResultType.success, data: formDesign };
      return data;
    } else {
      this.unviredSDK.logError(
        'dataService',
        'getNestedformDetailsFromDB',
        `Error while gettting  Nestedform Details From DB - ${fetchForm.message}`
      );
      let err = { type: ResultType.error, error: fetchForm.message };
      return err;
    }
  }
  async updatePermit(sendSubmissionHeaderToServer) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.RQST,
        '',
        sendSubmissionHeaderToServer,
        AppConstants.PA_PERMIT_PA_MODIFY_PERMIT,
        true
      );
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'updatePermit',
          'permit updated successfully.'
        );
        await this.unviredSDK.dbSaveWebData();
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'updatePermit',
          'Error while updating permit.'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getFormTemplates',
          'Error while updating permit : ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getFormTemplates',
        'Error while updating permit : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  async getUserById(customData: any) {
    console.log("customData in login page", customData )
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.QUERY,
        '',
        customData,
        AppConstants.PA_GET_USER,
        true
      );
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'getUser',
          'user Details has downloaded successfully.'
        );
        await this.unviredSDK.dbSaveWebData();
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'getUser',
          'Error while downloading user Details.'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getUser',
          'Error while downloading user Details : ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getUser',
        'Error while downloading user Details : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  async getAllUserData() {
    let customData = {
      USER: [
        {
          USER_HEADER: { USER_ID: '' },
        },
      ],
    };
    console.log("customData in login page", customData )
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.QUERY,
        '',
        customData,
        AppConstants.PA_GET_USER,
        true
      );
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'getUser',
          'user Details has downloaded successfully.'
        );
        await this.unviredSDK.dbSaveWebData();
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'getUser',
          'Error while downloading user Details.'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getUser',
          'Error while downloading user Details : ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getUser',
        'Error while downloading user Details : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  async getAllSkillData() {
    let customDataSkill = {
      SKILL: [
        {
          SKILL_HEADER: {
            USER_ID: '',
          },
        },
      ],
    };
    console.log("customData in login page", customDataSkill )
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.RQST,
        '',
        customDataSkill,
        AppConstants.PA_GET_SKLL,
        true
      );
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'getUser',
          'user Details has downloaded successfully.'
        );
        await this.unviredSDK.dbSaveWebData();
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'getUser',
          'Error while downloading skill Details.'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getUser',
          'Error while downloading skill Details : ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getUser',
        'Error while downloading user Details : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }



  async createAgentUser(customdata: any) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.QUERY,
        '',
        customdata,
        AppConstants.PA_CREATE_USER,
        true
      );
      if (result) {
        if (result.type == ResultType.success) {
          await this.unviredSDK.dbSaveWebData();
          return result;
        }
        if (result.type == ResultType.error) {
          return result.message.trim();
        }
      } else {
        return result;
      }
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'createAgentUsers',
        'Error while adding Agent users : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  async getDocument(customData) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.RQST,
        '',
        customData,
        AppConstants.PA_GET_DOCUMENT,
        true
      );
      if (result) {
        if (result.type == ResultType.success) {
          await this.unviredSDK.dbSaveWebData();
          if (result.data.InfoMessage === undefined) {
            this.unviredSDK.logInfo(
              'DataService',
              'getDocument',
              'Document downloaded Successfully'
            );
            return result.data.DOCUMENT[0];
          } else if (result.data.InfoMessage.length > 0) {
            return result.message.trim();
          }
        }
        if (result.type == ResultType.error) {
          if (result.code && result.code === 401) {
            this.unviredSDK.logError(
              'DataService',
              'getDocument',
              'Error downloading Document'
            );
            this.logoutToInitialPage();
          } else if (result.data.InfoMessage.length > 0) {
            return result.message.trim();
          }
        }
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getDocument',
          '"Error downloading Document" : ' + result.message
        );
        return 'Error downloading Document';
      }
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getDocument',
        'Error downloading Document : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }
  async getReportPermitList(permitQueryCtxHeader: PERMIT_QUERY_CTX_HEADER) {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.QUERY,
        '',
        permitQueryCtxHeader,
        AppConstants.PA_PERMIT_PA_REP_GET_PERMIT,
        true
      );
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'getPermitList',
          'got permit List for reports.'
        );
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'getPermitList',
          'Error while getting permit list for reports.'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getUserApprovalTypes',
          'Error while getting permit list for reports : ' + result.message
        );
      }
      let data = { type: ResultType.success, data: result.data, error: '' };
      return data;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getUserApprovalTypes',
        'Error while getting permit list for reports : ' + error
      );
      let err = { type: ResultType.error, error: error, data: '' };
      return err;
    }
  }
  // Modify Structure Status
  async modifyStructure(inputHeader: any) {
    try {
      let result = await this.unviredSDK.syncForeground(RequestType.RQST, inputHeader, '', AppConstants.PA_MODIFY_STRUCTURE, true);
      return result;
    } catch (error) {
      this.unviredSDK.logError('DataService', 'modifyStructure', 'ERROR: ' + error);
      let err = { "type": ResultType.error, "error": error };
      return err;
    }
  }
  setSelectedFacilityId(id) {
    this.selctedFacilityId = id;
  }
  getSelectedFacilityId() {
    return this.selctedFacilityId;
  }

  async getDashboard() {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.PULL,
        '',
        '',
        AppConstants.PERMIT_PA_GET_DASHBOARD,
        true
      );
      if (result.type == ResultType.success) {
        await this.unviredSDK.dbSaveWebData();
        this.unviredSDK.logInfo(
          'DataService',
          'getDashboard',
          'Dashboard has downloaded successfully.'
        );
      } else if (result.code && result.code === 401) {
        await this.logoutToInitialPage();
        this.unviredSDK.logError(
          'DataService',
          'getDashboard',
          'Error while downloading Dashboard'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getDashboard',
          'Error while downloading Dashboard : ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getDashboard',
        'Error while downloading Dashboard : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }


  async waitForAppInitialization(): Promise<void> {
    if (this.isInitialized) {
      return Promise.resolve();
    }

    if (!this.initializationPromise) {
      this.initializationPromise = new Promise((resolve) => {
        const checkInit = () => {
          if (this.isInitialized) {
            resolve();
          } else {
            setTimeout(checkInit, 100);
          }
        };
        checkInit();
      });
    }

    return this.initializationPromise;
  }

  setInitialized(value: boolean): void {
    this.isInitialized = value;
  }
  




}
