import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { Platform } from '@ionic/angular';

@Injectable({
  providedIn: 'root'
})
export class PlatformRedirectGuard implements CanActivate {
  
  constructor(
    private platform: Platform,
    private router: Router
  ) {}

  async canActivate(): Promise<boolean> {
    await this.platform.ready();
    
    // Detect if running in Cordova environment
    const isCordova = !!(window as any).cordova || !!(window as any).PhoneGap || !!(window as any).phonegap;
    
    if (isCordova) {
      // For mobile apps, redirect to mobile-login
      console.log('Mobile platform detected, redirecting to mobile-login');
      this.router.navigate(['/mobile-login'], { replaceUrl: true });
    } else {
      // For browser, redirect to login
      console.log('Browser platform detected, redirecting to login');
      this.router.navigate(['/login'], { replaceUrl: true });
    }
    
    return false; // Prevent the original route from activating
  }
}
