import { Component, NgZone, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { AlertController, LoadingController } from '@ionic/angular';
import { AppConstants } from '../../shared/app-constants';
import { HttpClient } from '@angular/common/http';
import { PopoverController } from '@ionic/angular';
import {
  AuthenticateActivateResult,
  AuthenticateAndActivateResultType,
  AuthenticateLocalResult,
  AuthenticateLocalResultType,
  LoginListenerType,
  LoginParameters,
  LoginType,
  ResultType,
  UnviredCordovaSDK,
} from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { DataService } from 'src/app/services/data.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { UserContextService } from 'src/app/services/user-context.service';


@Component({
  selector: 'app-contractor-login',
  templateUrl: './contractor-login.page.html',
  styleUrls: ['./contractor-login.page.scss'],
})
export class ContractorLoginPage implements OnInit {
  public constants: AppConstants;
  public appVersion: string = '';
  public versionHide: boolean = true;
  public language: any;
  public devicePlatform = 'browser';
  public busyIndicator: any = null;
  public emailId: string = '';
  public password: string = '';
  // public isDevice: boolean =false;
  public loginParameters: LoginParameters = null;
  // public url: string = null;
  public serverUrls: any = [];
  public selectedUrl: string;
  public loginResultType: LoginListenerType = null;
  public showAlert: any = null;
  // public showErrorMsg: boolean = false;
  // public loginErrMsg: string = '';
  public company: string = '';
  public errorMessage: string = '';
  public showErrorMessage: boolean = false;
  public messageColor: string;
  public progressbar: boolean = false;
  showPassword: boolean = false;
  public userFacility: string = '';

  

  constructor(
    private route: Router,
    public translate: TranslateService,
    private dataService: DataService,
    private unviredSDK: UnviredCordovaSDK,
    private loadingController: LoadingController,
    private alertController: AlertController,
    private httpClient: HttpClient,
    private ngZone: NgZone,
    public device: Device,
    public popoverController: PopoverController,
    private userContextService: UserContextService
  ) {}

  ngOnInit() {
    this.devicePlatform = this.device.platform;
    this.constants = new AppConstants();

    this.loginResultType = this.dataService.loginResultType;
    let url = location.href;
    this.selectedUrl = this.dataService.getUmpUrl(url);
    console.log('URL :  ' + this.selectedUrl);
    this.appVersion = this.dataService.getAppVersion();

    this.language =
      localStorage.getItem('locale') !== null
        ? localStorage.getItem('locale')
        : 'en';
  }

  //popover to select server
  // async getServers(ev: any) {
  //   const popover = await this.popoverController.create({
  //     component: ServerUrlsPage,
  //     event: ev,
  //     animated: true,
  //     showBackdrop: true,
  //     backdropDismiss: true,
  //     cssClass: 'popover-style',
  //     translucent: true,
  //     componentProps: {
  //       serverUrls: this.serverUrls,
  //     },
  //   });
  //   await popover.present();

  //   const { data } = await popover.onDidDismiss();
  //   if (data) this.selectedUrl = data;
  //   console.log('onDidDismiss resolved with role', data);
  // }


  async login() {
    // Busy indicator, when other processes are running
    this.busyIndicator = await this.loadingController.create({
      message: this.translate.instant('Signing in...'),
      spinner: this.translate.instant('crescent'),
      animated: true,
      showBackdrop: true,
      translucent: true,
    });

    if (
      this.language != null &&
      this.selectedUrl != null &&
      this.emailId != null &&
      this.password != null
    ) {
      try {
        // Set the arguments for ump
        this.loginParameters = new LoginParameters();

        // Ensure loginParameters is properly initialized before setting properties
        if (!this.loginParameters) {
          console.error('Failed to create LoginParameters instance');
          this.busyIndicator.dismiss();
          this.showErrorMessage = true;
          this.errorMessage = 'Login initialization failed. Please try again.';
          this.messageColor = 'danger';
          return;
        }

        this.loginParameters.url = this.selectedUrl;
        this.loginParameters.company = this.constants.LOGIN_PARAMS.COMPANY;
        this.loginParameters.loginType = LoginType.email;
        this.loginParameters.username = this.emailId.trim();
        this.loginParameters.password = this.password;
        this.loginParameters.jwtOptions = {
          app: 'PERMIT',
          language: this.language,
        };
        this.loginParameters.cacheWebData = true;

        // send selected language.
        this.loginParameters.loginLanguage = this.language;
      this.unviredSDK.logInfo(
          'Login',
          'login()',
          'Selected Language Code: ' + this.language
        );

        if(this.loginResultType === null  || this.loginResultType === undefined){
          this.loginResultType = 0;
        }

        try {
        switch (this.loginResultType) {
        
          // |Authenticate and Activate|
          case LoginListenerType.auth_activation_required:
            await this.busyIndicator.present();
            let authenticateActivateResult: AuthenticateActivateResult;
            try {
              try{
                authenticateActivateResult =
                await this.unviredSDK.authenticateAndActivate(
                  this.loginParameters
                );
              // |Authentication activated and Login success|
              }
              catch(err){
                console.log("error is " , err)
              }
           
              if (
                authenticateActivateResult.type ===
                AuthenticateAndActivateResultType.auth_activation_success
              ) {
                // this.setLocalDataToStorage();
                this.busyIndicator.dismiss();
                this.displayLandingPage();
                this.emailId = '';
                this.password = '';
              } else if (
                authenticateActivateResult.type ===
                AuthenticateAndActivateResultType.auth_activation_error
              ) {
                this.busyIndicator.dismiss();
                this.showErrorMessage = true;
                this.errorMessage = authenticateActivateResult.error || this.translate.instant('Login failed. Please check your credentials and try again.');
                this.messageColor = 'danger';
              }
            } catch (error) {
              this.busyIndicator.dismiss();
              this.showErrorMessage = true;
              this.errorMessage = this.translate.instant('Login failed. Please check your credentials and try again.');
              this.messageColor = 'danger';
              this.unviredSDK.logError(
                'LoginPage',
                'authenticateAndActivate()',
                'ERROR: ' + error
              );
            }
            break;

          // |Authenticate Locally|
          case LoginListenerType.app_requires_login:
            this.busyIndicator.present();
            let authenticateLocalResult: AuthenticateLocalResult;
            try {
              authenticateLocalResult = await this.unviredSDK.authenticateLocal(
                this.loginParameters
              );
              // |Authenticate (Local) credentials saved in database|

              if (
                authenticateLocalResult.type ===
                AuthenticateLocalResultType.login_success
              ) {
                // this.setLocalDataToStorage();
                this.busyIndicator.dismiss();
                this.displayLandingPage();
                this.emailId = '';
                this.password = '';
              } else if (
                authenticateLocalResult.type ===
                AuthenticateLocalResultType.login_error
              ) {
                this.busyIndicator.dismiss();
                this.showErrorMessage = true;
                this.errorMessage = authenticateLocalResult.error || this.translate.instant('Login failed. Please check your credentials and try again.');
                this.messageColor = 'danger';
              }
            } catch (error) {
              this.busyIndicator.dismiss();
              this.showErrorMessage = true;
              this.errorMessage = this.translate.instant('Login failed. Please check your credentials and try again.');
              this.messageColor = 'danger';
              this.unviredSDK.logError(
                'LoginPage',
                'authenticateLocal()',
                'ERROR: ' + error
              );
            }
            break;
        }
        } catch (error) {
          this.busyIndicator.dismiss();
          this.showErrorMessage = true;
          this.errorMessage = this.translate.instant('Login failed. Please check your credentials and try again.');
          this.messageColor = 'danger';
          this.unviredSDK.logError('LoginPage', 'login()', 'ERROR: ' + error);
        }
      } catch (error) {
        // Catch for the outer try block (loginParameters initialization)
        this.busyIndicator.dismiss();
        this.showErrorMessage = true;
        this.errorMessage = 'Login initialization failed. Please try again.';
        this.messageColor = 'danger';
        console.error('Login parameters initialization error:', error);
      }
    }
  }

  // Initialize all data needed from home.page.ts
  async initializeData() {
    // Display a loading message while initializing data
    const loading = await this.loadingController.create({
      message: this.translate.instant('Initializing application data...'),
      backdropDismiss: false
    });
    await loading.present();
    
    try {
      // Get user context and customization
      await this.loadcustomizationAndUserContext();
      
      // Get facility data
      await this.dataService.getFacility();
      
      // Get agent data
      await this.dataService.getAgents();
      await this.getUsersServerData();
      
      // Get user data
      await this.dataService.getAllUserData();
      
      // Get skill data
      await this.dataService.getAllSkillData();
      
      // Get structure data for the user facility
      let userContextResult = await this.dataService.getData('USER_CONTEXT_HEADER');
      if (userContextResult && userContextResult.length > 0) {
        this.userFacility = userContextResult[0]?.CURRENT_FACILITY || '';
        
        let customData = {
          STRUCTURE: [
            {
              STRUCTURE_HEADER: {
                FACILITY_ID: this.userFacility,
                DIVISION_ID: '',
                TAG: '',
                NAME: '',
                CATEGORY: '',
                STRUCT_TYPE: '',
                STATUS: '',
                P_MODE: '',
              },
            },
          ],
        };
        
        // Get structures for the user facility
        await this.dataService.getStructures(customData);
        
        // Download permits
        await this.dataService.downloadPermits(null, null, true);
      }
    } catch (error) {
      console.error('Error initializing application data:', error);
    } finally {
      // Close the loading indicator
      await loading.dismiss();
    }
  }

  async loadcustomizationAndUserContext() {
    try {
      // Get User Context
      let result: any = await this.dataService.getUserContext();
      if (result && result.type == ResultType.success) {
        await this.unviredSDK.dbSaveWebData();
        if (result?.data?.InfoMessage === undefined) {
          this.unviredSDK.logInfo(
            'ContractorLoginPage',
            'loadcustomizationAndUserContext',
            'User Context has been downloaded successfully.'
          );

          if (result?.data?.USER_CONTEXT?.length > 0) {
            localStorage.setItem(
              'userContext',
              JSON.stringify(result.data.USER_CONTEXT[0])
            );

            // Notify app component that user context has been updated
            this.userContextService.notifyUserContextUpdated();
          }
        }
      }

      // Get customization data
      await this.dataService.getCustomization();
    } catch (error) {
      console.error('Error loading customization and user context:', error);
      throw error;
    }
  }

  async getUsersServerData() {
    try {
      let userAgent = await this.dataService.getData('AGENT_HEADER');
      for (const element of userAgent) {
        let customData = {
          AGENT_USER: [
            {
              AGENT_USER_HEADER: {
                AGENT_ID: element?.AGENT_ID,
              },
            },
          ],
        };
        await this.dataService.getAgentUser(customData);
      }
    } catch (error) {
      console.error('Error getting users server data:', error);
      throw error;
    }
  }

  // Navigate to landing page
  displayLandingPage() {
    // Flag for do customization only when user navigates from login page.
    this.dataService.isStartCustomization = true;
    this.route.navigate(['permits']);
  }

  // Alert, when get any error response from ump
  async presentAlert(errorResponse: string) {
    this.showAlert = await this.alertController.create({
      header: this.translate.instant('Error'),
      message: errorResponse,
      animated: true,
      backdropDismiss: false,
      buttons: [
        {
          text: this.translate.instant('OK'),
        },
      ],
    });
    await this.showAlert.present();
  }

  gotoLogin() {
    this.route.navigate(['/login']);
  }

  forgetPassword(event) {
    event.preventDefault();

    //validate Email Id
    if (!this.emailId) {
      // Email Id is not present
      //display message asking for Email Id
      this.showErrorMessage = true;
      this.progressbar = false;
      this.errorMessage = this.translate.instant(
        'Email Id is required to reset password'
      );
      this.messageColor = 'danger';
      return;
    }

    //Email Id valid, disbale previous error messages if any
    console.log('Email Id is valid make api call');
    this.showErrorMessage = false;
    this.errorMessage = '';
    this.progressbar = true;
    //Email Id valid, make api call
    let EmailId = this.emailId;
    this.dataService
      .sendResetMail(EmailId)
      .toPromise()
      .then(
        async (response) => {
          this.messageColor = 'success';
          this.progressbar = false;
          if (response.status === 204) {
            this.ngZone.run(() => {
              this.showErrorMessage = true;
              this.errorMessage = this.translate.instant(
                'The reset password link has been sent to your email, please check your mailbox.'
              );
            });
          } else {
            this.showErrorMessage = true;
            this.errorMessage = this.translate.instant(
              'Email is not configured.You may wanna create account first.'
            );
          }
        },
        (error: any) => {
          console.log('API ERROR', error);
          this.showErrorMessage = true;
          this.progressbar = false;
          this.errorMessage = this.translate.instant(
            'Something went wrong please check company or email and try again.'
          );
          this.messageColor = 'danger';
        }
      );
  }

  onChangeDisableErrorMsg(event) {
    this.showErrorMessage = false;
    this.errorMessage = '';
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  checkAndLogin() {
    if (this.selectedUrl && this.emailId && this.password) {
      this.clearErrorMessage(); // Clear any previous error messages
      this.login();
    }
  }

  clearErrorMessage() {
    this.showErrorMessage = false;
    this.errorMessage = '';
  }

}
